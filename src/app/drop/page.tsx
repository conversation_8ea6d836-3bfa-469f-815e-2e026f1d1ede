"use client";

import SubmitCodeCard from "@/components/SubmitCode/SubmitCodeCard";
import { StyledScrollArea } from "@/components/ui/StyledFallbacks";
import { ABC_WHITE_PLUS_BOLD } from "@/utils/configs";

export default function Drop() {
  return (
    <StyledScrollArea style={{
      height: "calc(100vh - 68px)",
      display: "flex",
      justifyContent: "center",
      alignItems: "flex-start",
      width: "100%",
      padding: "1rem 0"
    }}>
      <div className="flex items-center justify-center flex-col-reverse md:flex-col gap-10 px-4 w-full">
        <div className="flex flex-col max-w-[720px] text-center gap-8 text-slate-300 [&>p]:text-sm mt-20">
          <h2
            style={ABC_WHITE_PLUS_BOLD.style}
            className="abc-white-plus text-3xl animate__animated animate__fadeIn delay-0-2s"
          >
            HARNESSING FOREST ENERGY
          </h2>
          <p>
            The core ybyCash protocol is a coordination system designed to programmatically mimmick nature onchain. The
            protocol achieves this by dynamically issuing a novel financial instrument that is generated via forest
            proof of work.
          </p>
          <p>
            Each day, Forest Oracles calculate the forest area of each ecoregion and pass this information to the Forest
            Central Bank, which mints an equivalent number of tokens to the forested area in each of the worlds’ 846
            terrestrial ecoregions.
          </p>
          <p>
            The central idea is that the protocol can act as a credibly neutral governance mechanism that dynamically
            adjusts daily token issuance based on real forest work, via the coordination of actors incentivized by token
            rewards.
          </p>
          <p>
            Units of the ybyCash currency are fungible within ecoregions. Each ecoregion has its own currency. Tokens
            are perpetual, they do not expire. Issuance is made daily
          </p>
        </div>

        <SubmitCodeCard />
      </div>
    </StyledScrollArea>
  );
}
