"use client";

import {
  StyledBadge,
  Styled<PERSON>ard,
  Styled<PERSON>ard<PERSON>ontent,
  StyledCardHeader,
  StyledCardTitle
} from "@/components/ui/StyledFallbacks";

const landUnits = [
  {
    id: 1,
    name: "Floresta Amazônica",
    size: 150,
    type: "Floresta Tropical",
    resources: ["Madeira", "Plantas Medicinais"],
    tokenPrice: 0.05,
    accumulatedTokens: 2500,
    icon: Tree,
  },
  {
    id: 2,
    name: "<PERSON> da Mantiqueira",
    size: 200,
    type: "Montanha",
    resources: ["Ecoturismo", "Água Mineral"],
    tokenPrice: 0.1,
    accumulatedTokens: 1500,
    icon: Mountain,
  },
  {
    id: 3,
    name: "Pantanal",
    size: 100,
    type: "Planície Alagada",
    resources: ["Biodiversidade", "Pesca"],
    tokenPrice: 0.08,
    accumulatedTokens: 3000,
    icon: Droplets,
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>",
    size: 300,
    type: "Savana",
    resources: ["Frutas Nativas", "Plantas Medicinais"],
    tokenPrice: 0.15,
    accumulatedTokens: 2000,
    icon: Sun,
  },
  {
    id: 5,
    name: "Chapada Diamantina",
    size: 180,
    type: "Planalto",
    resources: ["Minerais", "Turismo de Aventura"],
    tokenPrice: 0.07,
    accumulatedTokens: 1800,
    icon: Wind,
  },
  {
    id: 6,
    name: "Vale do São Francisco",
    size: 250,
    type: "Vale Fértil",
    resources: ["Vinicultura", "Fruticultura"],
    tokenPrice: 0.12,
    accumulatedTokens: 2200,
    icon: Sprout,
  },
  {
    id: 7,
    name: "Lençóis Maranhenses",
    size: 120,
    type: "Dunas e Lagoas",
    resources: ["Turismo", "Energia Eólica"],
    tokenPrice: 0.06,
    accumulatedTokens: 1000,
    icon: Map,
  },
  {
    id: 8,
    name: "Fernando de Noronha",
    size: 90,
    type: "Arquipélago",
    resources: ["Ecoturismo", "Preservação Marinha"],
    tokenPrice: 0.09,
    accumulatedTokens: 1200,
    icon: Palmtree,
  },
];

export default function LandUnits() {
  return (
    <div className="max-w-7xl mx-auto mt-5 px-4 sm:px-6 lg:px-8 max-[750px]:pb-20">
      <h1 className="text-4xl font-bold mb-8 text-white">Land units</h1>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {landUnits.map((unit) => {
          const Icon = unit.icon;
          return (
            <StyledCard
              key={unit.id}
              style={{
                background: "linear-gradient(to bottom, #0f172a, #1e293b)",
                borderColor: "#334155",
                color: "white",
                overflow: "hidden",
                display: "flex",
                justifyContent: "space-between",
                flexDirection: "column"
              }}
            >
              <StyledCardHeader style={{ paddingBottom: "0.5rem" }}>
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-start"
                }}>
                  <StyledCardTitle style={{
                    fontSize: "1.125rem",
                    fontWeight: "600"
                  }}>
                    {unit.name}
                  </StyledCardTitle>
                  <Icon style={{
                    height: "1.5rem",
                    width: "1.5rem",
                    color: "#10b981"
                  }} />
                </div>
                <p style={{
                  fontSize: "0.875rem",
                  color: "#94a3b8",
                  margin: 0
                }}>
                  {unit.type}
                </p>
              </StyledCardHeader>
              <StyledCardContent>
                <div style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(2, 1fr)",
                  gap: "0.5rem",
                  fontSize: "0.875rem",
                  marginBottom: "1rem"
                }}>
                  <div>
                    <p style={{ color: "#94a3b8", margin: 0 }}>Tamanho</p>
                    <p style={{ fontWeight: "500", margin: 0 }}>{unit.size} hectares</p>
                  </div>
                  <div>
                    <p style={{ color: "#94a3b8", margin: 0 }}>Recursos</p>
                    <p style={{ fontWeight: "500", margin: 0 }}>{unit.resources.join(", ")}</p>
                  </div>
                </div>
                <div style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "0.5rem",
                  marginTop: "1rem"
                }}>
                  <div style={{
                    display: "flex",
                    justifyContent: "space-between",
                    fontSize: "0.875rem"
                  }}>
                    <span style={{ color: "#94a3b8" }}>Preço do Token</span>
                    <span style={{ fontWeight: "500" }}>R$ {(unit.tokenPrice * 5).toFixed(2)}</span>
                  </div>
                  <div style={{
                    display: "flex",
                    justifyContent: "space-between",
                    fontSize: "0.875rem"
                  }}>
                    <span style={{ color: "#94a3b8" }}>Tokens Acumulados</span>
                    <span style={{ fontWeight: "500" }}>{unit.accumulatedTokens.toLocaleString()}</span>
                  </div>
                  <div style={{
                    display: "flex",
                    justifyContent: "space-between",
                    fontSize: "0.875rem"
                  }}>
                    <span style={{ color: "#94a3b8" }}>Valor Total</span>
                    <span style={{ fontWeight: "500" }}>
                      R$ {(unit.tokenPrice * unit.accumulatedTokens * 5).toFixed(2)}
                    </span>
                  </div>
                </div>
              </StyledCardContent>
              <div style={{
                display: "flex",
                flexWrap: "wrap",
                gap: "0.5rem",
                padding: "1rem"
              }}>
                {unit.resources.map((resource, index) => (
                  <StyledBadge
                    key={index}
                    variant="secondary"
                    style={{
                      backgroundColor: "#064e3b",
                      color: "#a7f3d0"
                    }}
                  >
                    {resource}
                  </StyledBadge>
                ))}
              </div>
            </StyledCard>
          );
        })}
      </div>
    </div>
  );
}
