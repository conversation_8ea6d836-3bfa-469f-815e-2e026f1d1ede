import React from "react";

import IbiIcon from "../IbiUi/IbiIcon";

const HoverCard = ({
  children,
  title,
  isVisible = true,
}: {
  children: React.ReactNode;
  title?: string;
  isVisible?: boolean;
}) => {
  return (
    <div style={{
      pointerEvents: "none",
      position: "relative",
      minHeight: isVisible ? "150px" : "0px"
    }}>
      {isVisible && (
        <div style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          overflow: "visible",
          borderRadius: "0 0 0.5rem 0.5rem",
          minWidth: "320px",
          width: "fit-content",
          height: "fit-content",
          minHeight: "150px",
          border: "1px solid rgba(255, 255, 255, 0.08)",
          backgroundColor: "rgba(12, 16, 51, 0.77)",
          opacity: 1,
          transform: "scale(1)",
          transition: "opacity 0.2s ease-out, transform 0.2s ease-out"
        }}>
          <div style={{
            borderRadius: "0.5rem",
            position: "absolute",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            left: "-3px",
            top: "-8px",
            width: "101.5%",
            height: "50px",
            backgroundColor: "#b4b0ff",
            color: "black",
            padding: "0 0.75rem"
          }}>
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "0.25rem"
            }}>
              <IbiIcon
                icon="jam:qr-code"
                style={{ fontSize: "0.875rem" }}
              />
              <h1 style={{
                fontWeight: "bold",
                margin: 0
              }}>
                {title}
              </h1>
            </div>
            <div style={{
              display: "flex",
              alignItems: "center",
              gap: "0.125rem"
            }}>
              <IbiIcon
                icon="tabler:square-filled"
                style={{ fontSize: "0.5rem" }}
              />
              <IbiIcon
                icon="tabler:square"
                style={{ fontSize: "0.5rem" }}
              />
              <IbiIcon
                icon="tabler:square"
                style={{ fontSize: "0.5rem" }}
              />
            </div>
          </div>
          <div style={{
            padding: "1rem",
            marginTop: "2.5rem"
          }}>
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

export default HoverCard;
