import { LabelProps } from "@radix-ui/react-label";
import React from "react";

import { StyledInput, StyledLabel } from "../../ui/StyledFallbacks";
import IbiIcon from "../IbiIcon";

interface IbiInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: string;
  iconPosition?: "left" | "right";
  id: string;
  label?: string;
  className?: string;
  wrapperClass?: string;
  labelProps?: LabelProps;
}

const IbiInput = ({
  id,
  icon,
  iconPosition = "left",
  label,
  className,
  wrapperClass,
  labelProps,
  ...props
}: IbiInputProps) => {
  return (
    <div
      style={{
        fontFamily: "denim",
        position: "relative",
        width: "100%",
        gap: "0.375rem",
        display: "flex",
        alignItems: "center",
        flexDirection: iconPosition === "right" ? "row-reverse" : "row"
      }}
    >
      {icon && <IbiIcon icon={icon} style={{ color: "white", fontSize: "1.125rem" }} />}
      <StyledLabel {...labelProps} htmlFor={id} style={{ flex: 1, marginTop: "-0.25rem" }}>
        <span>{label}</span>
        <StyledInput
          autoComplete="off"
          spellCheck="false"
          autoCorrect="off"
          {...props}
          id={id}
          style={{
            padding: 0,
            marginTop: "0.25rem",
          }}
          placeholder={props.placeholder}
        />
      </StyledLabel>
    </div>
  );
};

export default IbiInput;
