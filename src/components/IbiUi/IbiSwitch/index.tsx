import { StyledLabel, StyledSwitch } from "@/components/ui/StyledFallbacks";
import * as SwitchPrimitives from "@radix-ui/react-switch";
import React from "react";

interface IbiSwitchProps extends SwitchPrimitives.SwitchProps {
  id: string;
  label?: string;
}

const IbiSwitch = ({ id, label, ...props }: IbiSwitchProps) => {
  return (
    <div style={{
      display: "flex",
      alignItems: "center",
      gap: "0.5rem"
    }}>
      <StyledSwitch id={id} {...props} />
      <StyledLabel htmlFor={id}>{label}</StyledLabel>
    </div>
  );
};

export default IbiSwitch;
