import {
  StyledSheet,
  StyledSheetContent,
  StyledSheetHeader,
  StyledSheetTitle,
  StyledSheetTrigger,
  StyledButton
} from "@/components/ui/StyledFallbacks";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { ABC_WHITE_PLUS_BOLD, ABC_WHITE_PLUS_LIGHT } from "@/utils/configs";
import { User } from "@privy-io/react-auth";
import { usePathname } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import { useState } from "react";

import { shortenText } from "../../utils/formatting";
import { useAppHeaderSelector } from "../Headers/EntryHeader/header.store";
import IbiIcon from "../IbiUi/IbiIcon";

const AppMenu = () => {
  const { user } = usePrivyAuth();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  const handleRouterChange = (route: string) => {
    router.push(route);
    setOpen(false);
  };

  const highlighted = (route: string) => (pathname === route ? "bg-white text-black" : "");
  const menuOptions = [
    {
      name: "Claim",
      route: "/claim",
      icon: "subway:world-1",
    },
    {
      name: "Predict",
      route: "/predict",
      icon: "subway:water-1",
    },
    {
      name: "Dashboard",
      route: "/dashboard",
      icon: "ri:dashboard-fill",
    },
    {
      name: "Profile",
      route: "/profile",
      icon: "gg:profile",
    },
    {
      name: "Manage accounts",
      route: "/manage-accounts",
      icon: "mdi:accounts-group",
    },
    {
      name: "Invite codes",
      route: "/invite-codes",
      icon: "ri:share-box-line",
    },
    {
      name: "Settings",
      route: "/settings",
      icon: "material-symbols:settings-outline",
    },
    {
      name: "About page",
      route: "/",
      icon: "pepicons-pop:tree-circle-filled",
    },
  ];

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <StyledSheet open={open} onOpenChange={setOpen}>
        <StyledSheetTrigger>
          <IbiIcon
            icon="ion:reorder-two-outline"
            style={{
              fontSize: "1.875rem",
              opacity: 0.6,
              backgroundColor: "transparent",
              cursor: "pointer",
              transition: "opacity 0.2s"
            }}
            onMouseEnter={(e) => e.target.style.opacity = "1"}
            onMouseLeave={(e) => e.target.style.opacity = "0.6"}
          />
        </StyledSheetTrigger>
        <StyledSheetContent
          side="left"
          style={{
            backgroundColor: "#000",
            width: "280px",
            border: "none",
            display: "flex",
            flexDirection: "column"
          }}
        >
          <StyledSheetHeader>
            <StyledSheetTitle style={{ color: "white" }}>
              <CurrentUser user={user as User} />
            </StyledSheetTitle>
          </StyledSheetHeader>
          <div style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            flex: 1
          }}>
            <div>
              {menuOptions.map((option) => (
                <StyledButton
                  key={option.name}
                  style={{
                    backgroundColor: highlighted(option.route) ? "#fff" : "inherit",
                    display: "flex",
                    justifyContent: "flex-start",
                    boxShadow: "none",
                    gap: "0.5rem",
                    width: "100%",
                    padding: "0.75rem 1rem",
                    color: highlighted(option.route) ? "#000" : "#fff",
                    border: "none"
                  }}
                  onClick={() => handleRouterChange(option.route)}
                >
                  <IbiIcon icon={option.icon} />
                  <span style={ABC_WHITE_PLUS_LIGHT.style}>{option.name}</span>
                </StyledButton>
              ))}
            </div>
            <StyledButton
              style={{
                backgroundColor: "inherit",
                display: "flex",
                justifyContent: "flex-start",
                boxShadow: "none",
                gap: "0.5rem",
                width: "100%",
                padding: "0.75rem 1rem",
                color: "#fff",
                border: "none"
              }}
              onClick={toggleConfirmLogout}
            >
              <IbiIcon icon="el:off" />
              <span style={ABC_WHITE_PLUS_LIGHT.style}>Exit</span>
            </StyledButton>
          </div>
        </StyledSheetContent>
      </StyledSheet>
    </div>
  );
};

const CurrentUser = ({ user }: { user: User }) => {
  const serviceMap = {
    github: { name: user?.github?.name, icon: "fontisto:github" },
    google: { name: user?.google?.name, icon: "logos:google-icon" },
    apple: { name: user?.apple?.email, icon: "grommet-icons:apple" },
    discord: { name: user?.discord?.username, icon: "skill-icons:discord" },
    twitter: { name: user?.twitter?.username, icon: "akar-icons:twitter-fill" },
    wallet: { name: shortenText(user?.wallet?.address as string, 12), icon: "iconoir:wallet" },
  };

  const service = Object.entries(serviceMap).find(([, value]) => value.name);
  if (service) {
    return <UserViewWrapper name={service[1].name ?? ""} icon={service[1].icon} />;
  }
};

const UserViewWrapper = ({ name, icon }: { name: string; icon: string }) => {
  return (
    <div style={{
      display: "flex",
      alignItems: "center",
      gap: "0.75rem"
    }}>
      <IbiIcon
        icon={icon}
        style={{ fontSize: "1.5rem" }}
      />
      <div style={{
        display: "flex",
        flexDirection: "column",
        gap: "0.25rem"
      }}>
        <h1 style={{
          ...ABC_WHITE_PLUS_BOLD.style,
          fontSize: "0.875rem",
          fontWeight: "600",
          margin: 0
        }}>
          {name}
        </h1>
        <p style={{
          ...ABC_WHITE_PLUS_LIGHT.style,
          fontSize: "0.75rem",
          fontWeight: "normal",
          margin: 0
        }}>
          <strong>10</strong> points
        </p>
      </div>
    </div>
  );
};

export default AppMenu;
